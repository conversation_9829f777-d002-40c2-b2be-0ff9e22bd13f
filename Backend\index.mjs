/**
 * @fileoverview Unified Form Submission Handler for AWS Lambda
 * This AWS Lambda function handles multiple form submission types by:
 * 1. Determining form type based on request path or form data
 * 2. Routing to appropriate form processing logic
 * 3. Maintaining all existing functionality for each form type
 * 4. Providing a single entry point for AWS Lambda deployment
 *
 * Supported form types:
 * - Contact Us forms
 * - AI Readiness Assessment forms
 *
 * Configuration is loaded from AWS SSM Parameter Store with fallback to environment variables.
 *
 * <AUTHOR> Team
 * @version 3.0.0
 */

import sendDataToHubspot from "./common/sendDataToHubSpot.mjs";
import sendDataToSendGrid from "./common/sendDataToSendGrid.mjs";
import currentTimestamp from "./common/currentTimestamp.mjs";
import sendToSlack from "./common/sendDataToSlack.mjs";
import { getConfigValue } from "./common/ssmConfig.mjs";

/**
 * Determines the form type based on the request
 * @param {Object} event - AWS Lambda event object
 * @param {Object} formData - Parsed form data
 * @returns {string} Form type identifier
 */
function determineFormType(event, formData) {
  // Check if path contains form type identifier
  if (event.requestContext?.http?.path) {
    const path = event.requestContext.http.path.toLowerCase();
    if (path.includes("ai-readiness") || path.includes("ai_readiness")) {
      return "ai-readiness";
    }
    if (path.includes("contact-us") || path.includes("contact_us")) {
      return "contact-us";
    }
  }

  // Check for AI readiness specific fields in form data
  const aiReadinessFields = [
    "strategy___leadership",
    "talent___skills",
    "data_readiness___infrastructure",
    "impact_evaliation",
    "execution___monitoring",
    "average_of_all_score",
    "do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_",
  ];

  const hasAiFields = aiReadinessFields.some(
    (field) =>
      formData.hasOwnProperty(field) &&
      formData[field] !== undefined &&
      formData[field] !== ""
  );

  if (hasAiFields) {
    return "ai-readiness";
  }

  // Check for form type in the data itself
  if (formData.formType) {
    return formData.formType.toLowerCase();
  }

  // Check secondary_source for form identification
  if (formData.secondary_source) {
    const source = formData.secondary_source.toLowerCase();
    if (
      source.includes("ai") ||
      source.includes("readiness") ||
      source.includes("assessment")
    ) {
      return "ai-readiness";
    }
  }

  // Default to contact-us form
  return "contact-us";
}

/**
 * Builds form fields for Contact Us form
 * @param {Object} formData - Form data from request
 * @returns {Array} Array of form field objects
 */
function buildContactUsFormFields(formData) {
  return [
    { name: "firstname", value: formData?.firstName ?? "" },
    { name: "lastname", value: formData?.lastName ?? "" },
    { name: "email", value: formData?.emailAddress ?? "" },
    { name: "phone", value: formData?.phoneNumber ?? "" },
    {
      name: "how_did_you_hear_about_us_",
      value: formData?.howDidYouHearAboutUs ?? "",
    },
    { name: "company", value: formData?.companyName },
    { name: "message", value: formData?.howCanWeHelpYou },
    { name: "utm_source", value: formData?.utm_source ?? "" },
    { name: "utm_campaign", value: formData?.utm_campaign ?? "" },
    { name: "utm_medium", value: formData?.utm_medium ?? "" },
    { name: "clarity_link", value: formData?.clarity ?? "" },
    { name: "source_url", value: formData?.url ?? "" },
    { name: "referrer", value: formData?.referrer ?? "" },
    { name: "ip_address", value: formData?.ip_address ?? "" },
    { name: "city", value: formData?.city ?? "" },
    { name: "country", value: formData?.country ?? "" },
    { name: "ga_4_userid", value: formData?.ga_4_userid },
    { name: "source", value: formData?.secondary_source ?? "HomePage" },
    { name: "consent", value: formData?.consent ?? "" },
  ];
}

/**
 * Builds form fields for AI Readiness Assessment form
 * @param {Object} formData - Form data from request
 * @returns {Array} Array of form field objects
 */
function buildAiReadinessFormFields(formData) {
  return [
    { name: "firstname", value: formData?.firstName ?? "" },
    { name: "lastname", value: formData?.lastName ?? "" },
    { name: "email", value: formData?.emailAddress ?? "" },
    { name: "company", value: formData?.companyName },
    { name: "phone", value: formData?.phoneNumber ?? "" },
    { name: "city", value: formData?.city ?? "" },
    { name: "country", value: formData?.country ?? "" },
    { name: "ip_address", value: formData?.ip_address ?? "" },
    { name: "ga_4_userid", value: formData?.ga_4_userid },
    { name: "clarity_link", value: formData?.clarity ?? "" },
    { name: "source", value: formData?.secondary_source ?? "HomePage" },
    { name: "source_url", value: formData?.url ?? "" },
    { name: "utm_campaign", value: formData?.utm_campaign ?? "" },
    { name: "utm_source", value: formData?.utm_source ?? "" },
    { name: "utm_medium", value: formData?.utm_medium ?? "" },
    { name: "referrer", value: formData?.referrer ?? "" },
    { name: "consent", value: formData?.consent ?? "" },
    {
      name: "do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_",
      value:
        formData?.do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_ ??
        "",
    },
    {
      name: "how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_",
      value:
        formData?.how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_ ??
        "",
    },
    {
      name: "do_you_have_budget_allocated_for_your_ai_project_",
      value: formData?.do_you_have_budget_allocated_for_your_ai_project_ ?? "",
    },
    {
      name: "do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_",
      value:
        formData?.do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_ ??
        "",
    },
    {
      name: "which_of_the_below_db_tools_do_you_currently_use_",
      value: formData?.which_of_the_below_db_tools_do_you_currently_use_ ?? "",
    },
    {
      name: "is_the_relevant_data_for_the_ai_project_available_and_accessible_",
      value:
        formData?.is_the_relevant_data_for_the_ai_project_available_and_accessible_ ??
        "",
    },
    {
      name: "do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__",
      value:
        formData?.do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__ ??
        "",
    },
    {
      name: "how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib",
      value:
        formData?.how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib ??
        "",
    },
    {
      name: "does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_",
      value:
        formData?.does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_ ??
        "",
    },
    {
      name: "do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_",
      value:
        formData?.do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_ ??
        "",
    },
    {
      name: "do_you_have_risk_management_strategies_in_place_for_the_ai_project_",
      value:
        formData?.do_you_have_risk_management_strategies_in_place_for_the_ai_project_ ??
        "",
    },
    {
      name: "do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions",
      value:
        formData?.do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions ??
        "",
    },
    {
      name: "strategy___leadership",
      value: formData?.strategy___leadership ?? "",
    },
    {
      name: "talent___skills",
      value: formData?.talent___skills ?? "",
    },
    {
      name: "data_readiness___infrastructure",
      value: formData?.data_readiness___infrastructure ?? "",
    },
    {
      name: "impact_evaliation",
      value: formData?.impact_evaliation ?? "",
    },
    {
      name: "execution___monitoring",
      value: formData?.execution___monitoring ?? "",
    },
    {
      name: "average_of_all_score",
      value: formData?.average_of_all_score ?? "",
    },
  ];
}

/**
 * Processes form submission based on form type
 * @param {string} formType - Type of form being processed
 * @param {Object} formData - Form data from request
 * @returns {Promise<Object>} Processing result
 */
async function processFormSubmission(formType, formData) {
  let formFields;
  let hubspotFormGuidKey;
  let emailTemplateIdKey;

  // Determine form-specific configuration
  if (formType === "ai-readiness") {
    formFields = buildAiReadinessFormFields(formData);
    hubspotFormGuidKey = "HUBSPOT_AI_READINESS_FORM_GUID";
    emailTemplateIdKey = "SENDGRID_AI_READINESS_FORM_TEMPLATE_ID";
  } else {
    formFields = buildContactUsFormFields(formData);
    hubspotFormGuidKey = "HUBSPOT_GET_IN_TOUCH_FORM_GUID";
    emailTemplateIdKey = "SENDGRID_CONTACT_US_FORM_TEMPLATE_ID";
  }

  // Get configuration values from SSM
  const [hubspotApiKey, hubspotFormGuid] = await Promise.all([
    getConfigValue("HUBSPOT_API_KEY"),
    getConfigValue(hubspotFormGuidKey),
  ]);

  const payload = {
    fields: formFields,
    context: { pageUri: formData?.url },
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${hubspotApiKey}`,
    },
  };

  try {
    // Send Data to HubSpot
    const hubspotResponse = await sendDataToHubspot(
      formData?.secondary_source,
      payload,
      hubspotFormGuid
    );

    if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
      // Get email configuration from SSM
      const [mailTo, mailFrom, emailTemplateId] = await Promise.all([
        getConfigValue("MAIL_TO"),
        getConfigValue("MAIL_FROM"),
        getConfigValue(emailTemplateIdKey),
      ]);

      // Send Data to SendGrid if HubSpot submission is successful
      const emailRes = await sendDataToSendGrid(
        mailTo,
        mailFrom,
        formData?.emailAddress,
        emailTemplateId,
        formData
      );

      // Send Data to success Slack channel (webhook URL will be determined by sendToSlack)
      await sendToSlack(formData);

      console.log(currentTimestamp());
      console.log("Lead Data", formData);
      console.log("Form Type", formType);
      console.log("HubSpot Response", hubspotResponse);
      console.log("SendGrid Email Response", emailRes);
      console.log("------------------------------------");

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Form submitted successfully.",
          formType: formType,
          hubspotResponse: hubspotResponse.message,
        }),
      };
    } else {
      console.error("HubSpot Error:", hubspotResponse);

      let formLeadData = formData;
      formLeadData.page_name = formData?.secondary_source;
      formLeadData.failed_source = "Hubspot";

      // Get failure email configuration from SSM
      const [failureMailTo, failureMailFrom, failureTemplateId] =
        await Promise.all([
          getConfigValue("MAIL_TO"),
          getConfigValue("MAIL_FROM"),
          getConfigValue("SENDGRID_FAILURE_EMAIL_TEMPLATE_ID"),
        ]);

      const failureEmail = await sendDataToSendGrid(
        failureMailTo,
        failureMailFrom,
        formData?.emailAddress,
        failureTemplateId,
        formLeadData
      );

      // Send failure notification to Slack (webhook URL will be determined by sendToSlack)
      await sendToSlack(
        formData,
        undefined, // Let sendToSlack determine the webhook URL
        "⚠️ HubSpot Form Submission Failed ⚠️"
      );

      if (failureEmail.status) {
        console.error(`${formData?.secondary_source} form, failure email sent`);
      } else {
        console.error(
          `${formData?.secondary_source} form, failed to send failure email`
        );
      }

      return {
        statusCode: hubspotResponse?.status || 500,
        body: JSON.stringify({
          message: "Form submission failed.",
          formType: formType,
          error: hubspotResponse?.error || "Unknown error from HubSpot",
        }),
      };
    }
  } catch (error) {
    console.error("Error sending to HubSpot:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Internal server error while sending data to HubSpot",
        formType: formType,
        error: error.message || error,
      }),
    };
  }
}

/**
 * AWS Lambda handler for unified form submissions
 * Supports both Contact Us and AI Readiness Assessment forms
 *
 * @async
 * @function handler
 * @param {Object} event - AWS Lambda event object
 * @param {string} event.body - JSON string containing form data
 * @param {Object} event.headers - HTTP headers from the request
 * @param {Object} event.requestContext - AWS Lambda request context
 * @returns {Promise<Object>} Lambda response object with statusCode and body
 *
 * @example
 * // Example event.body structure for Contact Us:
 * {
 *   "firstName": "John",
 *   "lastName": "Doe",
 *   "emailAddress": "<EMAIL>",
 *   "phoneNumber": "+1234567890",
 *   "companyName": "Example Corp",
 *   "howCanWeHelpYou": "I need help with AI implementation",
 *   "howDidYouHearAboutUs": "Google Search",
 *   "consent": true,
 *   "url": "https://example.com/contact",
 *   "utm_source": "google",
 *   "utm_campaign": "ai-services",
 *   "utm_medium": "cpc"
 * }
 *
 * @example
 * // Example event.body structure for AI Readiness (includes contact fields plus AI-specific fields):
 * {
 *   "firstName": "Jane",
 *   "lastName": "Smith",
 *   "emailAddress": "<EMAIL>",
 *   "companyName": "Tech Corp",
 *   "do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_": "Yes, we have clear objectives",
 *   "strategy___leadership": "85",
 *   "budget___resources": "70",
 *   "talent___skills": "60",
 *   "data_readiness___infrastructure": "75",
 *   "impact_evaliation": "80",
 *   "execution___monitoring": "65",
 *   "average_of_all_score": "72.5"
 * }
 *
 * @example
 * // Success response:
 * {
 *   "statusCode": 200,
 *   "body": "{\"message\":\"Form submitted successfully.\",\"formType\":\"contact-us\",\"hubspotResponse\":\"Contact form data sent to HubSpot successfully.\"}"
 * }
 *
 * @example
 * // Error response:
 * {
 *   "statusCode": 500,
 *   "body": "{\"message\":\"Form submission failed.\",\"formType\":\"ai-readiness\",\"error\":\"HubSpot API error\"}"
 * }
 */
export const handler = async (event) => {
  try {
    // Parse form data from request body
    const formData = JSON.parse(event.body);

    // Determine which form type we're processing
    const formType = determineFormType(event, formData);

    console.log(`Processing ${formType} form submission`);
    console.log("Form Data:", formData);

    // Process the form submission based on type
    const result = await processFormSubmission(formType, formData);

    return result;
  } catch (error) {
    console.error("Error parsing request:", error);
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "Invalid request data",
        error: error.message || error,
      }),
    };
  }
};
