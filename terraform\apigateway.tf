#create the HTTP API Gateway
resource "aws_apigatewayv2_api" "my_api" {
  name          = "maruti_site_api"
  description   = "API for sending messages to Slack"
  protocol_type = "HTTP"

  # Enable CORS for the API Gateway

  cors_configuration {
    allow_credentials = false
    allow_headers     = ["*"]
    allow_methods     = ["*"]
    allow_origins     = ["*"]
    expose_headers    = []
    max_age           = 3600
  }
  tags = var.common_tags
}


# Integration with Lambda function
resource "aws_apigatewayv2_integration" "slack_integration" {
  api_id                 = aws_apigatewayv2_api.my_api.id
  integration_method     = "POST"
  integration_type       = "AWS_PROXY"
  payload_format_version = "2.0"
  integration_uri        = aws_lambda_function.mtl_site_function.invoke_arn
}
#Create Routes
resource "aws_apigatewayv2_route" "contact_us_route" {
  api_id    = aws_apigatewayv2_api.my_api.id
  route_key = "POST /contact-us"
  target    = "integrations/${aws_apigatewayv2_integration.slack_integration.id}"
}

resource "aws_apigatewayv2_route" "ai_readiness_route" {
  api_id    = aws_apigatewayv2_api.my_api.id
  route_key = "POST /ai-readiness"
  target    = "integrations/${aws_apigatewayv2_integration.slack_integration.id}"
}

# Create a deployment for the API
resource "aws_apigatewayv2_stage" "my_api_deployment" {
  depends_on  = [
    aws_apigatewayv2_integration.slack_integration,
    aws_apigatewayv2_route.contact_us_route,
    aws_apigatewayv2_route.ai_readiness_route
  ]
  api_id      = aws_apigatewayv2_api.my_api.id
  name        = "$default"
  auto_deploy = true

}
