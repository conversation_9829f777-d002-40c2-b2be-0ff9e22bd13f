# IAM Role for Lambda with SSM Permissions (no CloudWatch)
resource "aws_iam_role" "lambda_exec_role" {
  name = "lambda_exec_with_ssm"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = "sts:AssumeRole",
      Effect = "Allow",
      Principal = {
        Service = "lambda.amazonaws.com"
      }
    }]
  })
}

# Inline policy for accessing SSM Parameter Store
resource "aws_iam_role_policy" "lambda_ssm_policy" {
  name = "lambda_ssm_access"
  role = aws_iam_role.lambda_exec_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Action = [
        "ssm:Describe*",
        "ssm:Get*",
        "ssm:List*"
      ],
      Resource = "*"
    }]
  })
}

#allow only APIGateway to invoke the Lambda function
resource "aws_lambda_permission" "allow_apigateway" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.mtl_site_function.function_name
  principal     = "apigateway.amazonaws.com"

  # Specify the source ARN for the API Gateway
  source_arn = "${aws_apigatewayv2_api.my_api.execution_arn}/*/*"
}

# Archive the Python Lambda code
# data "archive_file" "lambda_zip" {
#   type        = "zip"
#   source_dir  = "${path.module}/lambda_function"
#   output_path = "${path.module}/lambda_function.zip"
# }

# fetching the SSM parameter
# data "aws_ssm_parameter" "slack_webhook_url" {
#   name            = aws_ssm_parameter.my_app_parameters.name
#   with_decryption = true
# }

# Lambda Function
resource "aws_lambda_function" "mtl_site_function" {
  function_name    = "mtl_site_function"
  runtime          = "nodejs18.x"
  role             = aws_iam_role.lambda_exec_role.arn
  handler          = "api/index.handler"
  source_code_hash = data.archive_file.file.output_base64sha256

  s3_bucket = aws_s3_bucket.lambda_code_bucket.bucket
  s3_key    = "lambda/my_lambda_function.zip"


  tags = var.common_tags
}