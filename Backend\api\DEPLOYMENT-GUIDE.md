# Global Form Handler Deployment Guide

## 🚀 Quick Start

This guide covers deploying the global unified form handler that consolidates all form submissions into a single, scalable AWS Lambda function.

## 📁 File Structure

```
Backend/api/
├── index.mjs                    # Global unified handler (main entry point)
├── test-global-handler.mjs      # Comprehensive test suite
├── README-GLOBAL-HANDLER.md     # Architecture documentation
├── FORM-TYPE-TEMPLATE.md        # Template for adding new forms
└── DEPLOYMENT-GUIDE.md          # This file
```

## 🔧 Configuration Changes

### 1. Terraform Updates

The Lambda handler has been updated:

```hcl
# terraform/lambda.tf
resource "aws_lambda_function" "mtl_site_function" {
  # ... other configuration
  handler = "api/index.handler"  # Changed from "api/contact-us/index.handler"
}
```

### 2. API Gateway Routes

Both form types now point to the same Lambda function:

```hcl
# terraform/apigateway.tf
resource "aws_apigatewayv2_route" "contact_us_route" {
  route_key = "POST /contact-us"
  target    = "integrations/${aws_apigatewayv2_integration.slack_integration.id}"
}

resource "aws_apigatewayv2_route" "ai_readiness_route" {
  route_key = "POST /ai-readiness"
  target    = "integrations/${aws_apigatewayv2_integration.slack_integration.id}"
}
```

## 🧪 Pre-Deployment Testing

### 1. Run Local Tests

```bash
cd Backend/api
node test-global-handler.mjs
```

Expected output:
```
🚀 Global Unified Form Handler Test Suite
==========================================

🧪 Running: Contact Us - Path Detection
   📋 Expected form type: contact-us
   📋 Actual form type: contact-us
   ✅ Form type detection: PASS
   📊 Status Code: 200
   ✅ Test Result: PASS

# ... more tests ...

📊 Test Summary
================
✅ Passed: 10/10
❌ Failed: 0/10

🎯 Overall Result: ALL TESTS PASSED
```

### 2. Validate Configuration

Ensure all required SSM parameters exist:

```bash
aws ssm get-parameter --name "/maruti_site/env" --with-decryption --region ap-south-1
```

Required parameters in the JSON:
- `NEXT_PUBLIC_HUBSPOT_API_KEY`
- `NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID`
- `NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID`
- `NEXT_PUBLIC_SENDGRID_*_TEMPLATE_ID` parameters
- `NEXT_PUBLIC_SLACK_*_WEBHOOK_URL` parameters
- `NEXT_PUBLIC_MAIL_TO` and `NEXT_PUBLIC_MAIL_FROM`

## 🚀 Deployment Steps

### 1. Deploy Infrastructure

```bash
cd terraform
terraform plan
terraform apply
```

Verify changes:
- Lambda handler updated to `api/index.handler`
- Both API Gateway routes point to the same Lambda function

### 2. Deploy Code

The existing CI/CD pipeline will automatically deploy the new code:

```bash
# Triggered automatically on push to development branch
# Or manually trigger the GitHub Action
```

### 3. Verify Deployment

Check Lambda function configuration in AWS Console:
- **Handler**: Should be `api/index.handler`
- **Runtime**: `nodejs18.x`
- **Code**: Should contain the new unified handler

## ✅ Post-Deployment Verification

### 1. Test API Endpoints

#### Contact Us Form:
```bash
curl -X POST https://your-api-gateway-url/contact-us \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Test",
    "lastName": "User",
    "emailAddress": "<EMAIL>",
    "companyName": "Test Corp",
    "howCanWeHelpYou": "Testing the new handler"
  }'
```

Expected response:
```json
{
  "message": "Form submitted successfully.",
  "formType": "contact-us",
  "formName": "Contact Us",
  "hubspotResponse": "Success message"
}
```

#### AI Readiness Form:
```bash
curl -X POST https://your-api-gateway-url/ai-readiness \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Test",
    "lastName": "User", 
    "emailAddress": "<EMAIL>",
    "companyName": "Test Corp",
    "strategy___leadership": "85",
    "average_of_all_score": "80"
  }'
```

Expected response:
```json
{
  "message": "Form submitted successfully.",
  "formType": "ai-readiness", 
  "formName": "AI Readiness Assessment",
  "hubspotResponse": "Success message"
}
```

### 2. Monitor CloudWatch Logs

Check Lambda function logs for:

```
🚀 Processing Contact Us form submission
📊 Supported form types: contact-us, ai-readiness
📝 Request path: /contact-us
🔍 Detected form type: contact-us
Form detection scores: {"contact-us": 100, "ai-readiness": 0}
⏱️ Processing completed in 1250ms
```

### 3. Verify Integrations

Confirm that all integrations work:
- [ ] **HubSpot**: Forms appear in HubSpot with correct data
- [ ] **SendGrid**: Confirmation emails are sent
- [ ] **Slack**: Success notifications appear in Slack channels

## 🔄 Rollback Plan

If issues are discovered:

### 1. Quick Rollback (Terraform)

```bash
cd terraform
git checkout HEAD~1 -- lambda.tf apigateway.tf
terraform apply
```

### 2. Code Rollback

Revert to previous commit and redeploy:

```bash
git revert <commit-hash>
git push origin development
```

### 3. Emergency Rollback

Manually update Lambda handler in AWS Console:
- Change handler back to `api/contact-us/index.handler`
- This will route all requests to the contact form handler temporarily

## 📊 Monitoring & Alerts

### Key Metrics to Monitor:

1. **Lambda Function**:
   - Invocation count
   - Error rate
   - Duration
   - Memory usage

2. **API Gateway**:
   - Request count per route
   - 4xx/5xx error rates
   - Latency

3. **Application Metrics**:
   - Form type detection accuracy
   - HubSpot success rate
   - Email delivery rate

### CloudWatch Alarms:

Set up alarms for:
- Lambda error rate > 5%
- API Gateway 5xx errors > 1%
- Lambda duration > 30 seconds

## 🧹 Cleanup (After Successful Deployment)

Once the global handler is verified to work correctly:

### 1. Remove Deprecated Files

```bash
# These files are no longer needed:
rm -rf Backend/api/contact-us/
rm -rf Backend/api/ai-readiness/
```

### 2. Update Documentation

- [ ] Update API documentation
- [ ] Update frontend integration guides
- [ ] Update team knowledge base

## 🔮 Future Enhancements

The global handler is designed for easy extension:

### Adding New Form Types:

1. Follow `FORM-TYPE-TEMPLATE.md`
2. Add configuration to `FORM_CONFIGS`
3. Add SSM parameters
4. Add test cases
5. Deploy and verify

### Potential New Forms:

- Newsletter signup
- Demo requests
- Partnership inquiries
- Support tickets
- Event registrations

## 📞 Support

If you encounter issues:

1. **Check Logs**: CloudWatch logs provide detailed error information
2. **Run Tests**: Use the test suite to identify issues
3. **Validate Config**: Ensure all SSM parameters are correct
4. **Monitor Metrics**: Check CloudWatch metrics for anomalies

## 🎯 Success Criteria

Deployment is successful when:

- [ ] All tests pass locally
- [ ] Infrastructure deploys without errors
- [ ] Both API endpoints respond correctly
- [ ] Form type detection works accurately
- [ ] All integrations (HubSpot, SendGrid, Slack) function properly
- [ ] CloudWatch logs show proper form processing
- [ ] No increase in error rates
- [ ] Response times remain acceptable

---

This global solution provides a robust, scalable foundation for handling all current and future form submission requirements while maintaining excellent performance and developer experience.
