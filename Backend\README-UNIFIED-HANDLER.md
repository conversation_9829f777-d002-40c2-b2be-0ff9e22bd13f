# Unified Form Handler for AWS Lambda

## Overview

This document describes the unified form submission handler that consolidates multiple form types into a single AWS Lambda function entry point. This approach is required because AWS Lambda functions can only have one handler function as specified by the handler property in the Lambda configuration.

## Architecture

### Before (Multiple Handlers)
```
Backend/
├── api/
│   ├── contact-us/
│   │   └── index.mjs (handler)
│   └── ai-readiness/
│       └── index.mjs (handler)
```

### After (Unified Handler)
```
Backend/
├── index.mjs (unified handler)
├── api/
│   ├── contact-us/
│   │   └── index.mjs (deprecated)
│   └── ai-readiness/
│       └── index.mjs (deprecated)
```

## How It Works

### 1. Form Type Detection
The unified handler automatically determines the form type using multiple strategies:

- **Path-based detection**: Checks the request path for keywords like `ai-readiness` or `contact-us`
- **Field-based detection**: Analyzes form data for AI-specific fields like `strategy___leadership`, `average_of_all_score`
- **Explicit form type**: Checks for a `formType` field in the form data
- **Source-based detection**: Examines the `secondary_source` field for form identification
- **Default fallback**: Defaults to `contact-us` form if no specific indicators are found

### 2. Form Processing
Once the form type is determined, the handler:

1. Builds appropriate form fields for the specific form type
2. Selects the correct HubSpot form GUID and email template
3. Processes the submission using the same logic as the original handlers
4. Maintains all existing functionality (HubSpot, SendGrid, Slack integration)

### 3. API Gateway Routes
The API Gateway is configured with multiple routes that all point to the same Lambda function:

- `POST /contact-us` - For contact form submissions
- `POST /ai-readiness` - For AI readiness assessment submissions

## Configuration Changes

### Lambda Function
- **Handler**: Changed from `api/contact-us/index.handler` to `index.handler`
- **Entry Point**: Now uses the unified `Backend/index.mjs` file

### API Gateway
- Added route for `POST /ai-readiness`
- Both routes point to the same Lambda integration

## Form Data Examples

### Contact Us Form
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "emailAddress": "<EMAIL>",
  "phoneNumber": "+1234567890",
  "companyName": "Example Corp",
  "howCanWeHelpYou": "I need help with AI implementation",
  "howDidYouHearAboutUs": "Google Search",
  "consent": true,
  "url": "https://example.com/contact",
  "utm_source": "google",
  "utm_campaign": "ai-services",
  "utm_medium": "cpc"
}
```

### AI Readiness Assessment Form
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "emailAddress": "<EMAIL>",
  "companyName": "Tech Corp",
  "do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_": "Yes",
  "strategy___leadership": "85",
  "talent___skills": "60",
  "data_readiness___infrastructure": "75",
  "impact_evaliation": "80",
  "execution___monitoring": "65",
  "average_of_all_score": "72.5"
}
```

## Testing

Run the test script to verify both form types work correctly:

```bash
cd Backend
node test-unified-handler.mjs
```

## Deployment

1. **Update Terraform**: The Lambda handler configuration has been updated to point to `index.handler`
2. **Deploy Infrastructure**: Run `terraform apply` to update the Lambda function and API Gateway routes
3. **Deploy Code**: The existing CI/CD pipeline will deploy the new unified handler

## Benefits

1. **Single Entry Point**: Complies with AWS Lambda's single-handler requirement
2. **Simplified Deployment**: One function to deploy and manage
3. **Consistent Processing**: Shared logic for error handling, logging, and integrations
4. **Easy Extension**: Adding new form types requires minimal changes
5. **Backward Compatibility**: Existing API endpoints continue to work

## Migration Notes

- The original handler files in `api/contact-us/` and `api/ai-readiness/` are now deprecated
- All form processing logic has been preserved in the unified handler
- No changes required to frontend form submissions
- All existing integrations (HubSpot, SendGrid, Slack) remain unchanged

## Troubleshooting

### Form Type Detection Issues
If forms are being processed as the wrong type:

1. Check the request path contains the correct keywords
2. Verify AI-specific fields are present in AI readiness forms
3. Add explicit `formType` field to form data if needed

### Configuration Issues
Ensure all required SSM parameters are available:
- `HUBSPOT_API_KEY`
- `HUBSPOT_GET_IN_TOUCH_FORM_GUID`
- `HUBSPOT_AI_READINESS_FORM_GUID`
- `SENDGRID_CONTACT_US_FORM_TEMPLATE_ID`
- `SENDGRID_AI_READINESS_FORM_TEMPLATE_ID`
- Other email and Slack configuration parameters
