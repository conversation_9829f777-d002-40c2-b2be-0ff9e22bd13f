/**
 * @fileoverview Test script for the unified form handler
 * This script tests both Contact Us and AI Readiness form processing
 */

import { handler } from './index.mjs';

// Test data for Contact Us form
const contactUsEvent = {
  body: JSON.stringify({
    firstName: "<PERSON>",
    lastName: "Doe",
    emailAddress: "<EMAIL>",
    phoneNumber: "+1234567890",
    companyName: "Example Corp",
    howCanWeHelpYou: "I need help with AI implementation",
    howDidYouHearAboutUs: "Google Search",
    consent: true,
    url: "https://example.com/contact",
    utm_source: "google",
    utm_campaign: "ai-services",
    utm_medium: "cpc",
    secondary_source: "ContactUs"
  }),
  requestContext: {
    http: {
      path: "/contact-us"
    }
  }
};

// Test data for AI Readiness form
const aiReadinessEvent = {
  body: JSON.stringify({
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    emailAddress: "<EMAIL>",
    companyName: "Tech Corp",
    do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_: "Yes, we have clear objectives",
    strategy___leadership: "85",
    talent___skills: "60",
    data_readiness___infrastructure: "75",
    impact_evaliation: "80",
    execution___monitoring: "65",
    average_of_all_score: "72.5",
    secondary_source: "AIReadiness"
  }),
  requestContext: {
    http: {
      path: "/ai-readiness"
    }
  }
};

async function runTests() {
  console.log("🧪 Testing Unified Form Handler");
  console.log("================================");

  try {
    console.log("\n📝 Testing Contact Us Form...");
    const contactResult = await handler(contactUsEvent);
    console.log("Contact Us Result:", JSON.stringify(contactResult, null, 2));

    console.log("\n🤖 Testing AI Readiness Form...");
    const aiResult = await handler(aiReadinessEvent);
    console.log("AI Readiness Result:", JSON.stringify(aiResult, null, 2));

  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Only run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests();
}

export { contactUsEvent, aiReadinessEvent, runTests };
