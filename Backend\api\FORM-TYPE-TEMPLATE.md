# Form Type Template

Use this template to add new form types to the global unified handler.

## Step-by-Step Guide

### 1. Define Form Configuration

Add your new form type to the `FORM_CONFIGS` object in `Backend/api/index.mjs`:

```javascript
'your-form-type': {
  name: 'Your Form Display Name',
  hubspotFormGuidKey: 'HUBSPOT_YOUR_FORM_GUID',
  emailTemplateIdKey: 'SENDGRID_YOUR_FORM_TEMPLATE_ID',
  detectionRules: {
    pathKeywords: ['your-form', 'your_form', 'yourform'],
    requiredFields: ['firstName', 'lastName', 'emailAddress'],
    uniqueFields: ['yourUniqueField1', 'yourUniqueField2'],
    sourceKeywords: ['your', 'form', 'source']
  },
  fieldMapping: (formData) => [
    // Common fields
    { name: "firstname", value: formData?.firstName ?? "" },
    { name: "lastname", value: formData?.lastName ?? "" },
    { name: "email", value: formData?.emailAddress ?? "" },
    { name: "company", value: formData?.companyName ?? "" },
    { name: "phone", value: formData?.phoneNumber ?? "" },
    
    // Tracking fields
    { name: "source", value: formData?.secondary_source ?? "HomePage" },
    { name: "source_url", value: formData?.url ?? "" },
    { name: "utm_source", value: formData?.utm_source ?? "" },
    { name: "utm_campaign", value: formData?.utm_campaign ?? "" },
    { name: "utm_medium", value: formData?.utm_medium ?? "" },
    { name: "referrer", value: formData?.referrer ?? "" },
    { name: "ip_address", value: formData?.ip_address ?? "" },
    { name: "city", value: formData?.city ?? "" },
    { name: "country", value: formData?.country ?? "" },
    { name: "ga_4_userid", value: formData?.ga_4_userid },
    { name: "clarity_link", value: formData?.clarity ?? "" },
    { name: "consent", value: formData?.consent ?? "" },
    
    // Your form-specific fields
    { name: "your_custom_field_1", value: formData?.yourCustomField1 ?? "" },
    { name: "your_custom_field_2", value: formData?.yourCustomField2 ?? "" },
    // Add more fields as needed
  ]
}
```

### 2. Configuration Checklist

#### Detection Rules:
- [ ] **pathKeywords**: URL path segments that identify this form type
- [ ] **requiredFields**: Fields that must be present for form to be valid
- [ ] **uniqueFields**: Fields that are unique to this form type (for detection)
- [ ] **sourceKeywords**: Keywords in `secondary_source` that identify this form

#### Field Mapping:
- [ ] Include all common fields (name, email, company, etc.)
- [ ] Include all tracking fields (UTM, analytics, etc.)
- [ ] Map your form-specific fields
- [ ] Use proper HubSpot field names
- [ ] Provide default values with `?? ""`

### 3. Add SSM Parameters

Add these parameters to your SSM Parameter Store at `/maruti_site/env`:

```json
{
  "NEXT_PUBLIC_HUBSPOT_YOUR_FORM_GUID": "your-hubspot-form-guid-here",
  "NEXT_PUBLIC_SENDGRID_YOUR_FORM_TEMPLATE_ID": "your-sendgrid-template-id-here"
}
```

### 4. Add API Gateway Route (Optional)

If you want a dedicated endpoint, add to `terraform/apigateway.tf`:

```hcl
resource "aws_apigatewayv2_route" "your_form_route" {
  api_id    = aws_apigatewayv2_api.my_api.id
  route_key = "POST /your-form"
  target    = "integrations/${aws_apigatewayv2_integration.slack_integration.id}"
}
```

And update the deployment dependencies:

```hcl
resource "aws_apigatewayv2_stage" "my_api_deployment" {
  depends_on  = [
    aws_apigatewayv2_integration.slack_integration,
    aws_apigatewayv2_route.contact_us_route,
    aws_apigatewayv2_route.ai_readiness_route,
    aws_apigatewayv2_route.your_form_route  # Add this line
  ]
  # ... rest of configuration
}
```

### 5. Create Test Cases

Add test cases to `Backend/api/test-global-handler.mjs`:

```javascript
const yourFormTestCases = [
  {
    name: "Your Form - Path Detection",
    event: {
      body: JSON.stringify({
        firstName: "Test",
        lastName: "User",
        emailAddress: "<EMAIL>",
        yourUniqueField1: "unique value",
        secondary_source: "YourForm"
      }),
      requestContext: {
        http: { path: "/your-form" }
      }
    },
    expectedFormType: "your-form-type"
  },
  {
    name: "Your Form - Field Detection",
    event: {
      body: JSON.stringify({
        firstName: "Test",
        lastName: "User", 
        emailAddress: "<EMAIL>",
        yourUniqueField1: "unique value",
        yourUniqueField2: "another unique value"
      }),
      requestContext: {
        http: { path: "/api/forms" }
      }
    },
    expectedFormType: "your-form-type"
  }
];

// Add to allTestCases array
const allTestCases = [
  ...contactUsTestCases,
  ...aiReadinessTestCases,
  ...explicitTypeTestCases,
  ...validationErrorTestCases,
  ...edgeCaseTestCases,
  ...yourFormTestCases  // Add this line
];
```

### 6. Test Your Implementation

```bash
cd Backend/api
node test-global-handler.mjs
```

Verify that:
- [ ] Your form type is detected correctly
- [ ] Path-based detection works
- [ ] Field-based detection works
- [ ] Validation passes for valid data
- [ ] Validation fails for invalid data

### 7. Example Form Data Structure

Document the expected form data structure:

```javascript
// Example form data for your form type
{
  // Required fields
  "firstName": "John",
  "lastName": "Doe", 
  "emailAddress": "<EMAIL>",
  
  // Optional common fields
  "companyName": "Example Corp",
  "phoneNumber": "+1234567890",
  
  // Your form-specific fields
  "yourUniqueField1": "specific value",
  "yourUniqueField2": "another value",
  
  // Tracking fields (automatically included)
  "url": "https://example.com/your-form",
  "utm_source": "google",
  "utm_campaign": "your-campaign",
  "utm_medium": "cpc",
  "secondary_source": "YourFormPage",
  "consent": true,
  
  // Optional explicit form type
  "formType": "your-form-type"
}
```

## Common Patterns

### Newsletter Signup Example:
```javascript
'newsletter-signup': {
  name: 'Newsletter Signup',
  hubspotFormGuidKey: 'HUBSPOT_NEWSLETTER_FORM_GUID',
  emailTemplateIdKey: 'SENDGRID_NEWSLETTER_TEMPLATE_ID',
  detectionRules: {
    pathKeywords: ['newsletter', 'subscribe', 'signup'],
    requiredFields: ['emailAddress'],
    uniqueFields: ['subscriptionType', 'frequency'],
    sourceKeywords: ['newsletter', 'subscribe']
  },
  fieldMapping: (formData) => [
    { name: "email", value: formData?.emailAddress ?? "" },
    { name: "subscription_type", value: formData?.subscriptionType ?? "" },
    { name: "frequency", value: formData?.frequency ?? "weekly" },
    // ... other fields
  ]
}
```

### Demo Request Example:
```javascript
'demo-request': {
  name: 'Demo Request',
  hubspotFormGuidKey: 'HUBSPOT_DEMO_FORM_GUID', 
  emailTemplateIdKey: 'SENDGRID_DEMO_TEMPLATE_ID',
  detectionRules: {
    pathKeywords: ['demo', 'request-demo', 'book-demo'],
    requiredFields: ['firstName', 'lastName', 'emailAddress', 'companyName'],
    uniqueFields: ['preferredDate', 'teamSize', 'useCase'],
    sourceKeywords: ['demo', 'booking']
  },
  fieldMapping: (formData) => [
    { name: "firstname", value: formData?.firstName ?? "" },
    { name: "lastname", value: formData?.lastName ?? "" },
    { name: "email", value: formData?.emailAddress ?? "" },
    { name: "company", value: formData?.companyName ?? "" },
    { name: "preferred_date", value: formData?.preferredDate ?? "" },
    { name: "team_size", value: formData?.teamSize ?? "" },
    { name: "use_case", value: formData?.useCase ?? "" },
    // ... other fields
  ]
}
```

## Validation

After adding your form type, ensure:

1. **Detection Works**: Form is correctly identified
2. **Processing Works**: Data flows to HubSpot correctly  
3. **Email Works**: Confirmation emails are sent
4. **Slack Works**: Notifications are sent
5. **Error Handling**: Failures are handled gracefully
6. **Logging**: Proper logs are generated

## Deployment

1. Update the code in `Backend/api/index.mjs`
2. Add SSM parameters
3. Update Terraform if adding new routes
4. Deploy via CI/CD pipeline
5. Test in staging environment
6. Monitor logs after production deployment

---

Following this template ensures consistency and maintainability across all form types in the global unified handler.
