#!/usr/bin/env node

/**
 * @fileoverview Test runner for the global unified form handler
 * This script runs the comprehensive test suite from the Backend root directory
 */

import { runAllTests } from './api/test-global-handler.mjs';

console.log('🚀 Starting Global Form Handler Tests...\n');

try {
  const results = await runAllTests();
  
  if (results.passed === results.total) {
    console.log('\n🎉 All tests passed! The global handler is ready for deployment.');
    process.exit(0);
  } else {
    console.log('\n❌ Some tests failed. Please review the errors above.');
    process.exit(1);
  }
} catch (error) {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
}
