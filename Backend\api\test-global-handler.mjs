/**
 * @fileoverview Comprehensive test suite for the global unified form handler
 * Tests multiple form types, detection strategies, and edge cases
 */

import { handler } from './index.mjs';

// Test data for Contact Us form
const contactUsTestCases = [
  {
    name: "Contact Us - Path Detection",
    event: {
      body: JSON.stringify({
        firstName: "<PERSON>",
        lastName: "Doe",
        emailAddress: "<EMAIL>",
        phoneNumber: "+1234567890",
        companyName: "Example Corp",
        howCanWeHelpYou: "I need help with AI implementation",
        howDidYouHearAboutUs: "Google Search",
        consent: true,
        url: "https://example.com/contact",
        utm_source: "google",
        utm_campaign: "ai-services",
        utm_medium: "cpc",
        secondary_source: "ContactUs"
      }),
      requestContext: {
        http: { path: "/contact-us" }
      }
    },
    expectedFormType: "contact-us"
  },
  {
    name: "Contact Us - Field Detection",
    event: {
      body: JSON.stringify({
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        emailAddress: "<EMAIL>",
        companyName: "Tech Corp",
        howCanWeHelpYou: "Need consultation",
        howDidYouHearAboutUs: "LinkedIn",
        secondary_source: "HomePage"
      }),
      requestContext: {
        http: { path: "/api/forms" }
      }
    },
    expectedFormType: "contact-us"
  }
];

// Test data for AI Readiness Assessment form
const aiReadinessTestCases = [
  {
    name: "AI Readiness - Path Detection",
    event: {
      body: JSON.stringify({
        firstName: "Alice",
        lastName: "Johnson",
        emailAddress: "<EMAIL>",
        companyName: "Tech Corp",
        do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_: "Yes, we have clear objectives",
        strategy___leadership: "85",
        talent___skills: "60",
        data_readiness___infrastructure: "75",
        impact_evaliation: "80",
        execution___monitoring: "65",
        average_of_all_score: "72.5",
        secondary_source: "AIReadiness"
      }),
      requestContext: {
        http: { path: "/ai-readiness" }
      }
    },
    expectedFormType: "ai-readiness"
  },
  {
    name: "AI Readiness - Field Detection",
    event: {
      body: JSON.stringify({
        firstName: "Bob",
        lastName: "Wilson",
        emailAddress: "<EMAIL>",
        companyName: "AI Startup",
        strategy___leadership: "90",
        talent___skills: "70",
        data_readiness___infrastructure: "80",
        impact_evaliation: "85",
        execution___monitoring: "75",
        average_of_all_score: "80.0",
        secondary_source: "Assessment"
      }),
      requestContext: {
        http: { path: "/forms" }
      }
    },
    expectedFormType: "ai-readiness"
  }
];

// Test cases for explicit form type specification
const explicitTypeTestCases = [
  {
    name: "Explicit Form Type - Contact Us",
    event: {
      body: JSON.stringify({
        formType: "contact-us",
        firstName: "Charlie",
        lastName: "Brown",
        emailAddress: "<EMAIL>",
        companyName: "Example Inc",
        howCanWeHelpYou: "General inquiry"
      }),
      requestContext: {
        http: { path: "/api/submit" }
      }
    },
    expectedFormType: "contact-us"
  },
  {
    name: "Explicit Form Type - AI Readiness",
    event: {
      body: JSON.stringify({
        formType: "ai-readiness",
        firstName: "Diana",
        lastName: "Prince",
        emailAddress: "<EMAIL>",
        companyName: "Wonder Corp",
        strategy___leadership: "95"
      }),
      requestContext: {
        http: { path: "/api/submit" }
      }
    },
    expectedFormType: "ai-readiness"
  }
];

// Test cases for validation errors
const validationErrorTestCases = [
  {
    name: "Missing Required Fields",
    event: {
      body: JSON.stringify({
        emailAddress: "<EMAIL>"
        // Missing firstName and lastName
      }),
      requestContext: {
        http: { path: "/contact-us" }
      }
    },
    expectedStatusCode: 400
  },
  {
    name: "Invalid Email Format",
    event: {
      body: JSON.stringify({
        firstName: "Test",
        lastName: "User",
        emailAddress: "invalid-email-format",
        companyName: "Test Corp"
      }),
      requestContext: {
        http: { path: "/contact-us" }
      }
    },
    expectedStatusCode: 400
  }
];

// Test cases for edge cases
const edgeCaseTestCases = [
  {
    name: "Empty Form Data",
    event: {
      body: JSON.stringify({}),
      requestContext: {
        http: { path: "/contact-us" }
      }
    },
    expectedStatusCode: 400
  },
  {
    name: "Invalid JSON",
    event: {
      body: "{ invalid json }",
      requestContext: {
        http: { path: "/contact-us" }
      }
    },
    expectedStatusCode: 400
  }
];

/**
 * Run a single test case
 * @param {Object} testCase - Test case object
 * @returns {Promise<Object>} Test result
 */
async function runTestCase(testCase) {
  try {
    console.log(`\n🧪 Running: ${testCase.name}`);
    const result = await handler(testCase.event);
    
    const success = testCase.expectedStatusCode 
      ? result.statusCode === testCase.expectedStatusCode
      : result.statusCode === 200;
    
    if (testCase.expectedFormType && result.statusCode === 200) {
      const responseBody = JSON.parse(result.body);
      const formTypeMatch = responseBody.formType === testCase.expectedFormType;
      console.log(`   📋 Expected form type: ${testCase.expectedFormType}`);
      console.log(`   📋 Actual form type: ${responseBody.formType}`);
      console.log(`   ${formTypeMatch ? '✅' : '❌'} Form type detection: ${formTypeMatch ? 'PASS' : 'FAIL'}`);
    }
    
    console.log(`   📊 Status Code: ${result.statusCode}`);
    console.log(`   ${success ? '✅' : '❌'} Test Result: ${success ? 'PASS' : 'FAIL'}`);
    
    if (!success) {
      console.log(`   📝 Response: ${JSON.stringify(result, null, 2)}`);
    }
    
    return { name: testCase.name, success, result };
  } catch (error) {
    console.log(`   ❌ Test Error: ${error.message}`);
    return { name: testCase.name, success: false, error: error.message };
  }
}

/**
 * Run all test suites
 */
async function runAllTests() {
  console.log("🚀 Global Unified Form Handler Test Suite");
  console.log("==========================================");

  const allTestCases = [
    ...contactUsTestCases,
    ...aiReadinessTestCases,
    ...explicitTypeTestCases,
    ...validationErrorTestCases,
    ...edgeCaseTestCases
  ];

  const results = [];
  
  for (const testCase of allTestCases) {
    const result = await runTestCase(testCase);
    results.push(result);
  }

  // Summary
  console.log("\n📊 Test Summary");
  console.log("================");
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (total - passed > 0) {
    console.log("\n❌ Failed Tests:");
    results.filter(r => !r.success).forEach(r => {
      console.log(`   - ${r.name}: ${r.error || 'Assertion failed'}`);
    });
  }

  console.log(`\n🎯 Overall Result: ${passed === total ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  return { passed, total, results };
}

// Only run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}

export { 
  contactUsTestCases, 
  aiReadinessTestCases, 
  explicitTypeTestCases,
  validationErrorTestCases,
  edgeCaseTestCases,
  runAllTests,
  runTestCase
};
