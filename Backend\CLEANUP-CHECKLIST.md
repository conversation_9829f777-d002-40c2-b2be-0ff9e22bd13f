# Cleanup Checklist for Unified Handler Migration

## Files Created ✅

### New Files
- `Backend/index.mjs` - Unified form handler (main entry point)
- `Backend/test-unified-handler.mjs` - Test script for verification
- `Backend/README-UNIFIED-HANDLER.md` - Documentation
- `Backend/CLEANUP-CHECKLIST.md` - This checklist

### Modified Files
- `terraform/lambda.tf` - Updated handler from `api/contact-us/index.handler` to `index.handler`
- `terraform/apigateway.tf` - Added AI readiness route and updated dependencies

## Verification Steps ✅

Before removing any files, verify the unified handler works correctly:

### 1. Test the Unified Handler
```bash
cd Backend
node test-unified-handler.mjs
```

### 2. Deploy and Test
1. Deploy the updated Terraform configuration:
   ```bash
   cd terraform
   terraform plan
   terraform apply
   ```

2. Test both API endpoints:
   - `POST /contact-us` - Should process contact forms
   - `POST /ai-readiness` - Should process AI readiness forms

3. Verify in AWS Lambda console that the handler is set to `index.handler`

### 3. Monitor Logs
Check CloudWatch logs to ensure:
- Form type detection works correctly
- Both form types process successfully
- All integrations (HubSpot, SendGrid, Slack) function properly

## Files to Remove After Verification ⚠️

**⚠️ IMPORTANT: Only remove these files AFTER confirming the unified handler works correctly in production**

### Deprecated Handler Files
```bash
# Remove the old individual handlers
rm -rf Backend/api/contact-us/
rm -rf Backend/api/ai-readiness/
```

### Files to Remove:
- `Backend/api/contact-us/index.mjs` - Original contact form handler
- `Backend/api/ai-readiness/index.mjs` - Original AI readiness handler
- `Backend/api/contact-us/` - Directory (if empty)
- `Backend/api/ai-readiness/` - Directory (if empty)
- `Backend/api/` - Directory (if empty after removing subdirectories)

### Optional Cleanup Files
After successful deployment and verification:
- `Backend/test-unified-handler.mjs` - Test script (can be kept for future testing)
- `Backend/README-UNIFIED-HANDLER.md` - Documentation (recommended to keep)
- `Backend/CLEANUP-CHECKLIST.md` - This checklist (can be removed after cleanup)

## Rollback Plan 🔄

If issues are discovered with the unified handler:

### 1. Revert Terraform Changes
```bash
cd terraform
git checkout HEAD~1 -- lambda.tf apigateway.tf
terraform apply
```

### 2. Restore Original Handler Files
```bash
cd Backend
git checkout HEAD~1 -- api/
```

### 3. Alternative: Keep Both Approaches
- Keep the unified handler as `Backend/index.mjs`
- Keep original handlers in `Backend/api/`
- Switch between them by updating the Terraform handler configuration

## Post-Cleanup Verification ✅

After removing deprecated files:

1. **Build Test**: Ensure the application builds without errors
2. **Deployment Test**: Verify CI/CD pipeline works with new structure
3. **Functional Test**: Test both form types in production
4. **Monitoring**: Monitor error rates and logs for any issues

## Benefits Achieved 🎯

- ✅ Single Lambda entry point (AWS requirement compliance)
- ✅ Simplified deployment and maintenance
- ✅ Consistent error handling and logging
- ✅ Preserved all existing functionality
- ✅ Backward compatible API endpoints
- ✅ Easy to extend for new form types

## Notes

- The `Backend/common/` directory remains unchanged and is still used by the unified handler
- All SSM configuration and integrations remain the same
- Frontend applications require no changes
- API Gateway endpoints remain the same (`/contact-us` and `/ai-readiness`)

---

**Remember**: Always test thoroughly in a staging environment before applying changes to production!
